name: Process Pending Payouts - Production

on:
  schedule:
    - cron: "*/15 * * * *" # Runs every 15 minutes
  workflow_dispatch: # Allows manual triggering

jobs:
  process-payouts:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "20.12.0"
      - uses: pnpm/action-setup@v2
        with:
          version: 8
      - name: Install dependencies
        run: pnpm install
      - name: Process pending payouts
        run: cd packages/server && pnpm run cron:process-payouts
        env:
          DB_URL: ${{ secrets.DB_URL }}
          STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
          # Add any other required secrets here
